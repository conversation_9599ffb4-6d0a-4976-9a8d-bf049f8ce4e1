{"example.main": {"id": "example.main", "component_type": "function", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\example.py", "relative_path": "example.py", "depends_on": ["example.item", "example.i", "example.datetime", "example.Decimal", "vending_machine.SysErr", "example.e", "vending_machine.Sys", "example.pos", "example.ret", "example.<PERSON><PERSON><PERSON>", "models.product.Item"], "start_line": 8, "end_line": 34, "has_docstring": false, "docstring": ""}, "vending_machine.SysErr": {"id": "vending_machine.SysErr", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": [], "start_line": 9, "end_line": 10, "has_docstring": false, "docstring": ""}, "vending_machine.Sys": {"id": "vending_machine.Sys", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": ["payment.payment_processor.TxStatus", "payment.payment_processor.Handler", "vending_machine.h", "vending_machine.Decimal", "vending_machine.Optional", "vending_machine.Sys.add_money", "payment.payment_processor.Tx", "vending_machine.List", "models.product.Item", "vending_machine.item", "vending_machine.Sys.pick", "vending_machine.SysErr", "payment.payment_processor.Cash", "inventory.inventory_manager.Store", "vending_machine.Sys.buy", "vending_machine.<PERSON>ple", "vending_machine.Sys.ls", "vending_machine.amt", "vending_machine.Sys.cancel", "vending_machine.x"], "start_line": 13, "end_line": 65, "has_docstring": false, "docstring": ""}, "vending_machine.Sys.__init__": {"id": "vending_machine.Sys.__init__", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": ["vending_machine.Optional", "payment.payment_processor.Handler", "payment.payment_processor.Cash", "payment.payment_processor.Tx", "inventory.inventory_manager.Store"], "start_line": 15, "end_line": 18, "has_docstring": false, "docstring": ""}, "vending_machine.Sys.ls": {"id": "vending_machine.Sys.ls", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": ["vending_machine.<PERSON>ple", "vending_machine.item", "vending_machine.x", "vending_machine.List", "models.product.Item"], "start_line": 20, "end_line": 26, "has_docstring": false, "docstring": ""}, "vending_machine.Sys.pick": {"id": "vending_machine.Sys.pick", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": ["vending_machine.SysErr", "vending_machine.Optional", "models.product.Item"], "start_line": 28, "end_line": 34, "has_docstring": false, "docstring": ""}, "vending_machine.Sys.add_money": {"id": "vending_machine.Sys.add_money", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": ["payment.payment_processor.Cash", "vending_machine.Decimal", "vending_machine.SysErr"], "start_line": 36, "end_line": 39, "has_docstring": false, "docstring": ""}, "vending_machine.Sys.buy": {"id": "vending_machine.Sys.buy", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": ["vending_machine.<PERSON>ple", "vending_machine.Decimal", "payment.payment_processor.TxStatus", "vending_machine.SysErr", "vending_machine.Optional", "payment.payment_processor.Cash", "models.product.Item"], "start_line": 41, "end_line": 53, "has_docstring": false, "docstring": ""}, "vending_machine.Sys.cancel": {"id": "vending_machine.Sys.cancel", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\vending_machine.py", "relative_path": "vending_machine.py", "depends_on": ["payment.payment_processor.Cash", "vending_machine.Decimal", "vending_machine.Optional", "vending_machine.SysErr"], "start_line": 55, "end_line": 65, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store": {"id": "inventory.inventory_manager.Store", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": ["inventory.inventory_manager.Store.get_at", "inventory.inventory_manager.Store.find", "inventory.inventory_manager.Store.put", "inventory.inventory_manager.Store.get", "inventory.inventory_manager.Store.rm", "inventory.inventory_manager.Store.ls", "models.product.Item"], "start_line": 6, "end_line": 59, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store.__init__": {"id": "inventory.inventory_manager.Store.__init__", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": ["models.product.Item"], "start_line": 8, "end_line": 11, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store.put": {"id": "inventory.inventory_manager.Store.put", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": ["models.product.Item"], "start_line": 13, "end_line": 32, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store.rm": {"id": "inventory.inventory_manager.Store.rm", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": [], "start_line": 34, "end_line": 41, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store.get": {"id": "inventory.inventory_manager.Store.get", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": ["models.product.Item"], "start_line": 43, "end_line": 44, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store.get_at": {"id": "inventory.inventory_manager.Store.get_at", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": ["models.product.Item"], "start_line": 46, "end_line": 50, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store.ls": {"id": "inventory.inventory_manager.Store.ls", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": ["models.product.Item"], "start_line": 52, "end_line": 53, "has_docstring": false, "docstring": ""}, "inventory.inventory_manager.Store.find": {"id": "inventory.inventory_manager.Store.find", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\inventory\\inventory_manager.py", "relative_path": "inventory\\inventory_manager.py", "depends_on": [], "start_line": 55, "end_line": 59, "has_docstring": false, "docstring": ""}, "models.product.Item": {"id": "models.product.Item", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\models\\product.py", "relative_path": "models\\product.py", "depends_on": ["models.product.Item.check", "models.product.Item.mod"], "start_line": 7, "end_line": 113, "has_docstring": true, "docstring": "\n    Summary:\n    Represents an item with associated attributes for tracking and management in various contexts.\n\n    Description:\n    This class serves as a blueprint for creating items that can be tracked and managed within a system. Each item has attributes such as a unique code, a label, a value, a count, an optional expiration date, and a group classification. The primary motivation behind this class is to facilitate resource management, inventory tracking, or any scenario where items need to be monitored for validity and availability.\n\n    Use this class when you need to represent items that may have a limited lifespan or quantity, such as in inventory systems, gaming resources, or token management. It provides methods to check the validity of an item and to modify its count, ensuring that operations on the item are safe and consistent.\n\n    The class fits into larger systems by allowing for easy integration with resource management workflows, enabling developers to track item states and manage their lifecycle effectively.\n\n    Example:\n    ```python\n    from datetime import datetime, timedelta\n\n    # Create an item with a specific expiration date\n    item = Item(code='A123', label='Sample Item', val=10.0, count=5, exp=datetime.now() + timedelta(days=1))\n\n    # Check if the item is valid\n    is_valid = item.check()  # Returns True if count > 0 and not expired\n\n    # Modify the count of the item\n    item.mod(2)  # Decreases count by 2, returns True\n    ```\n\n    Parameters:\n    - code (str): A unique identifier for the item.\n    - label (str): A descriptive name for the item.\n    - val (float): The value associated with the item, representing its worth.\n    - count (int): The quantity of the item available. Must be a non-negative integer.\n    - exp (Optional[datetime]): An optional expiration date for the item. If set, the item will be considered invalid after this date.\n    - grp (str): A classification group for the item, defaulting to 'misc'.\n\n    Attributes:\n    - code (str): The unique identifier for the item.\n    - label (str): The name or description of the item.\n    - val (float): The monetary or functional value of the item.\n    - count (int): The current quantity of the item available, must be non-negative.\n    - exp (Optional[datetime]): The expiration date of the item, if applicable.\n    - grp (str): The group classification of the item, useful for categorization.\n    "}, "models.product.Item.check": {"id": "models.product.Item.check", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\models\\product.py", "relative_path": "models\\product.py", "depends_on": [], "start_line": 56, "end_line": 77, "has_docstring": true, "docstring": "\n        Validates the current object's state based on count and expiration.\n\n        Checks whether the object is still valid by verifying two key conditions:\n        1. The object's count is greater than zero\n        2. The object has not exceeded its expiration timestamp\n\n        This method is typically used to determine if an object is still usable\n        or has become stale/invalid. It provides a quick state validation check\n        that can be used in resource management, token validation, or lifecycle\n        tracking scenarios.\n\n        Returns:\n            bool: True if the object is valid (count > 0 and not expired),\n                  False otherwise.\n        "}, "models.product.Item.mod": {"id": "models.product.Item.mod", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\models\\product.py", "relative_path": "models\\product.py", "depends_on": [], "start_line": 79, "end_line": 113, "has_docstring": true, "docstring": "\n        Summary:\n        Determines if the current count can be decremented by a specified value.\n\n        Description:\n        This method checks if the `count` attribute is greater than or equal to the provided integer `n`. If so, it decrements `count` by `n` and returns `True`. If `count` is less than `n`, it returns `False`, indicating that the operation could not be performed.\n\n        Use this function when managing resources or operations that require a controlled decrement of a count, ensuring that the count does not drop below zero. This is particularly useful in scenarios such as resource allocation, gaming mechanics, or iterative processes.\n\n        The method is integral to classes that require precise control over a count, allowing for safe decrements while maintaining the integrity of the count value.\n\n        Args:\n        n (int, optional): The value to decrement from `count`. Must be a positive integer that does not exceed the current `count`. Default is 1.\n\n        Returns:\n        bool: Returns `True` if the decrement was successful (i.e., `count` was greater than or equal to `n`), otherwise returns `False`.\n\n        Raises:\n        No exceptions are raised by this method. Ensure that `n` is a positive integer and does not exceed the current `count` to avoid logical errors.\n\n        Examples:\n        ```python\n        obj = YourClass()\n        obj.count = 5\n        result = obj.mod(2)  # result will be True, obj.count will be 3\n        result = obj.mod(4)  # result will be False, obj.count remains 3\n        result = obj.mod(0)  # result will be False, as n should be greater than 0\n        result = obj.mod(-1) # result will be False, as n should be a positive integer\n        ```\n        "}, "payment.payment_processor.TxStatus": {"id": "payment.payment_processor.TxStatus", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": [], "start_line": 9, "end_line": 13, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Tx": {"id": "payment.payment_processor.Tx", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": ["payment.payment_processor.TxStatus"], "start_line": 17, "end_line": 22, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Handler": {"id": "payment.payment_processor.Handler", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": ["payment.payment_processor.Handler.proc", "payment.payment_processor.Tx", "payment.payment_processor.Handler.rev"], "start_line": 25, "end_line": 33, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Handler.proc": {"id": "payment.payment_processor.Handler.proc", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": ["payment.payment_processor.Tx"], "start_line": 28, "end_line": 29, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Handler.rev": {"id": "payment.payment_processor.Handler.rev", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": ["payment.payment_processor.Tx"], "start_line": 32, "end_line": 33, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Cash": {"id": "payment.payment_processor.Cash", "component_type": "class", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": ["payment.payment_processor.TxStatus", "payment.payment_processor.Cash.rev", "payment.payment_processor.Cash.ret", "payment.payment_processor.Cash.add", "payment.payment_processor.Handler", "payment.payment_processor.Cash.proc", "payment.payment_processor.Tx"], "start_line": 36, "end_line": 62, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Cash.__init__": {"id": "payment.payment_processor.Cash.__init__", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": [], "start_line": 38, "end_line": 39, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Cash.add": {"id": "payment.payment_processor.Cash.add", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": [], "start_line": 41, "end_line": 42, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Cash.proc": {"id": "payment.payment_processor.Cash.proc", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": ["payment.payment_processor.TxStatus", "payment.payment_processor.Tx"], "start_line": 44, "end_line": 50, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Cash.rev": {"id": "payment.payment_processor.Cash.rev", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": ["payment.payment_processor.TxStatus", "payment.payment_processor.Tx"], "start_line": 52, "end_line": 57, "has_docstring": false, "docstring": ""}, "payment.payment_processor.Cash.ret": {"id": "payment.payment_processor.Cash.ret", "component_type": "method", "file_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\docagent\\data\\raw_test_repo\\payment\\payment_processor.py", "relative_path": "payment\\payment_processor.py", "depends_on": [], "start_line": 59, "end_line": 62, "has_docstring": false, "docstring": ""}}