#!/usr/bin/env python
# Copyright (c) Meta Platforms, Inc. and affiliates
"""
Script to evaluate the helpfulness of docstrings generated by different systems.

Usage:
    conda activate docstringgen
    python src/evaluate_helpfulness.py
"""

import os
import yaml
import argparse
import sys
from pathlib import Path

# Add the src directory to the path so we can import modules
src_dir = Path(__file__).parent.parent
sys.path.insert(0, str(src_dir))

from src.evaluator.helpfulness_evaluator import DocstringHelpfulnessEvaluator

def main():
    parser = argparse.ArgumentParser(description="Evaluate docstring helpfulness")
    parser.add_argument("--data-path", type=str, 
                        default="experiments/eval/results/completeness_evaluation_cleaned.json",
                        help="Path to the completeness evaluation data")
    parser.add_argument("--output-dir", type=str, 
                        default="experiments/eval/results/helpfulness",
                        help="Directory to store evaluation results")
    parser.add_argument("--n-samples", type=int, default=50,
                        help="Number of components to sample")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed for reproducibility")
    parser.add_argument("--model", type=str, default=None,
                        help="LLM model to use (defaults to model in config)")
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Get configuration
    config_path = "config/agent_config.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Get API key and model from config
    api_key = config["llm"]["api_key"]
    model = args.model or config["llm"]["model"]
    
    print(f"Using model: {model}")
    print(f"Sampling {args.n_samples} components with seed {args.seed}")
    
    # Initialize evaluator
    evaluator = DocstringHelpfulnessEvaluator(
        data_path=args.data_path,
        output_dir=args.output_dir,
        api_key=api_key,
        model=model
    )
    
    # Run evaluation
    results = evaluator.run_evaluation(
        n_samples=args.n_samples,
        seed=args.seed
    )
    
    # Print summary
    print("\n=== Evaluation Complete ===")
    print(f"Results saved to {args.output_dir}")
    print(f"Total evaluations: {len(results['results'])}")
    
    # Calculate average score
    scores = [r["score"] for r in results["results"]]
    avg_score = sum(scores) / len(scores) if scores else 0
    print(f"Overall average score: {avg_score:.2f}")
    
    # Calculate average by system
    systems = evaluator.SYSTEMS
    for system in systems:
        system_scores = [r["score"] for r in results["results"] if r["system"] == system]
        if system_scores:
            avg = sum(system_scores) / len(system_scores)
            print(f"{system}: {avg:.2f} (n={len(system_scores)})")

if __name__ == "__main__":
    main() 