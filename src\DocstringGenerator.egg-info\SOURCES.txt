LICENSE
README.md
setup.py
src/DocstringGenerator.egg-info/PKG-INFO
src/DocstringGenerator.egg-info/SOURCES.txt
src/DocstringGenerator.egg-info/dependency_links.txt
src/DocstringGenerator.egg-info/requires.txt
src/DocstringGenerator.egg-info/top_level.txt
src/agent/__init__.py
src/agent/base.py
src/agent/orchestrator.py
src/agent/reader.py
src/agent/searcher.py
src/agent/verifier.py
src/agent/workflow.py
src/agent/writer.py
src/agent/llm/__init__.py
src/agent/llm/base.py
src/agent/llm/claude_llm.py
src/agent/llm/factory.py
src/agent/llm/gemini_llm.py
src/agent/llm/huggingface_llm.py
src/agent/llm/openai_llm.py
src/agent/llm/rate_limiter.py
src/dependency_analyzer/__init__.py
src/dependency_analyzer/ast_parser.py
src/dependency_analyzer/topo_sort.py
src/evaluator/__init__.py
src/evaluator/base.py
src/evaluator/completeness.py
src/evaluator/evaluation_common.py
src/evaluator/helpfulness_attributes.py
src/evaluator/helpfulness_description.py
src/evaluator/helpfulness_evaluator.py
src/evaluator/helpfulness_evaluator_ablation.py
src/evaluator/helpfulness_examples.py
src/evaluator/helpfulness_parameters.py
src/evaluator/helpfulness_summary.py
src/evaluator/segment.py
src/evaluator/truthfulness.py
src/visualizer/__init__.py
src/visualizer/progress.py
src/visualizer/status.py
src/visualizer/web_bridge.py
src/web/__init__.py
src/web/app.py
src/web/config_handler.py
src/web/process_handler.py
src/web/run.py
src/web/visualization_handler.py